import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

export interface PDFModalState {
  isOpen: boolean
  pdfPath: string | null
  pdfName: string | null
  initialPage: number
  pageRange: {
    start: number
    end: number
  } | null
}

const initialState: PDFModalState = {
  isOpen: false,
  pdfPath: null,
  pdfName: null,
  initialPage: 1,
  pageRange: null,
}

export interface OpenPDFModalPayload {
  pdfPath: string
  pdfName: string
  initialPage?: number
  pageRange?: {
    start: number
    end: number
  }
}

const pdfModalSlice = createSlice({
  name: "pdfModal",
  initialState,
  reducers: {
    openPDFModal(state, action: PayloadAction<OpenPDFModalPayload>) {
      state.isOpen = true
      state.pdfPath = action.payload.pdfPath
      state.pdfName = action.payload.pdfName
      state.initialPage = action.payload.initialPage || 1
      state.pageRange = action.payload.pageRange || null
    },
    closePDFModal(state) {
      state.isOpen = false
      state.pdfPath = null
      state.pdfName = null
      state.initialPage = 1
      state.pageRange = null
    },
    setPDFPage(state, action: PayloadAction<number>) {
      state.initialPage = action.payload
    },
  },
})

export const { openPDFModal, closePDFModal, setPDFPage } = pdfModalSlice.actions
export default pdfModalSlice.reducer
