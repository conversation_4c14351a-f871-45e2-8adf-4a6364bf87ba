import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { Query, Message } from "@/types"


// Removed mock data - queries will be created from real user interactions

interface QueriesState {
  queries: Query[]
  activeQueryId: string | null
}

const initialState: QueriesState = {
  queries: [],
  activeQueryId: null,
}

const queriesSlice = createSlice({
  name: "queries",
  initialState,
  reducers: {
    addQuery(state, action: PayloadAction<Query>) {
      if (!state.queries.find((q) => q.id === action.payload.id)) {
        state.queries.unshift(action.payload)
      }
    },
    // Adds a message to a specific query
    addMessageToQuery(state, action: PayloadAction<{ queryId: string; message: Message }>) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        query.messages.push(action.payload.message)
      }
    },
    // Updates a specific message within a query (e.g., its thinkingState or final content)
    updateMessageInQuery(
      state,
      action: PayloadAction<{ queryId: string; messageId: string; updatedMessageData: Partial<Message> }>,
    ) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === action.payload.messageId)
        if (messageIndex !== -1) {
          query.messages[messageIndex] = { ...query.messages[messageIndex], ...action.payload.updatedMessageData }
        }
      }
    },
    // Updates thinking step for a specific message in a specific query
    updateMessageThinkingStep(
      state,
      action: PayloadAction<{
        queryId: string
        messageId: string
        agentName: 'user_intent_agent' | 'web_search_agent' | 'product_unstructured_agent' | 'database_search_agent' | 'feedback_agent' | 'supervisor_agent'
        status: 'idle' | 'in-progress' | 'completed' | 'error'
        message: string
        result: any
        displayName: string
        error?: string
      }>
    ) {
      const { queryId, messageId, agentName, status, message, result, displayName, error } = action.payload
      const query = state.queries.find((q) => q.id === queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === messageId)
        if (messageIndex !== -1) {
          const currentMessage = query.messages[messageIndex]
          if (!currentMessage.thinkingSteps) {
            currentMessage.thinkingSteps = {}
          }
          currentMessage.thinkingSteps[agentName] = {
            status,
            message,
            result,
            displayName,
            error
          }
        }
      }
    },
    // Sets processing error for a specific message in a specific query
    setMessageProcessingError(
      state,
      action: PayloadAction<{ queryId: string; messageId: string; error: string | null }>
    ) {
      const { queryId, messageId, error } = action.payload
      const query = state.queries.find((q) => q.id === queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === messageId)
        if (messageIndex !== -1) {
          query.messages[messageIndex].processingError = error
        }
      }
    },
    // Resets thinking steps for a specific message in a specific query
    resetMessageThinkingSteps(
      state,
      action: PayloadAction<{ queryId: string; messageId: string }>
    ) {
      const { queryId, messageId } = action.payload
      const query = state.queries.find((q) => q.id === queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === messageId)
        if (messageIndex !== -1) {
          query.messages[messageIndex].thinkingSteps = {}
          query.messages[messageIndex].processingError = null
        }
      }
    },
    deleteQuery(state, action: PayloadAction<string>) {
      state.queries = state.queries.filter((q) => q.id !== action.payload)
      if (state.activeQueryId === action.payload) {
        state.activeQueryId = null
      }
    },
    setActiveQueryId(state, action: PayloadAction<string | null>) {
      state.activeQueryId = action.payload
    },
  },
})

export const {
  addQuery,
  addMessageToQuery,
  updateMessageInQuery,
  deleteQuery,
  setActiveQueryId,
  updateMessageThinkingStep,
  setMessageProcessingError,
  resetMessageThinkingSteps
} = queriesSlice.actions
export default queriesSlice.reducer
