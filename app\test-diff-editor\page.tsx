'use client';

import React, { useState } from 'react';
import LiveDiffEditor from '@/components/LiveDiffEditor';

const TestDiffEditorPage = () => {
  const [savedContent, setSavedContent] = useState('');

  const initialContent = `# Welcome to the Enhanced Diff Editor

This is a **sample document** with some initial content to demonstrate the enhanced diff editor capabilities.

## Features

- Rich text editing with markdown support
- Split view layout for better editing experience
- Real-time diff preview
- Smooth animations and transitions

### Sample List
1. First item
2. Second item
3. Third item

You can edit this content and see the changes highlighted in real-time!

> This is a blockquote to show formatting capabilities.

\`\`\`javascript
// Sample code block
function hello() {
  console.log("Hello, World!");
}
\`\`\`

[Link to documentation](https://example.com)
`;

  const handleSave = (content: string) => {
    setSavedContent(content);
    console.log('Content saved:', content);
  };

  const handleCancel = () => {
    console.log('Edit cancelled');
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Enhanced Diff Editor Test</h1>
      
      <div className="space-y-8">
        {/* Standard Editor */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Standard Editor</h2>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your content here..."
              className="min-h-[200px]"
            />
          </div>
        </div>

        {/* Rich Text Editor */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Rich Text Editor</h2>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your content here..."
              className="min-h-[200px]"
              enableRichText={true}
            />
          </div>
        </div>

        {/* Rich Text Editor with Split View */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Rich Text Editor with Split View</h2>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your content here..."
              className="min-h-[200px]"
              enableRichText={true}
              splitView={true}
            />
          </div>
        </div>

        {/* Read-only Display */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Read-only Display</h2>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              editable={false}
              className="min-h-[200px]"
            />
          </div>
        </div>

        {/* Saved Content Display */}
        {savedContent && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Last Saved Content (Markdown)</h2>
            <div className="border rounded-lg p-4 bg-gray-50">
              <pre className="whitespace-pre-wrap text-sm">{savedContent}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestDiffEditorPage;
