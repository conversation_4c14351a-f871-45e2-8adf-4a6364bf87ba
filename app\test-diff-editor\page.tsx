'use client';

import React, { useState } from 'react';
import LiveDiffEditor from '@/components/LiveDiffEditor';

const TestDiffEditorPage = () => {
  const [savedContent, setSavedContent] = useState('');

  const initialContent = `# Welcome to the Enhanced Diff Editor

This is a **sample document** with some initial content to demonstrate the enhanced diff editor capabilities.

## Features

- Rich text editing with markdown support
- Split view layout for better editing experience
- Real-time diff preview
- Smooth animations and transitions

### Sample List
1. First item
2. Second item
3. Third item

You can edit this content and see the changes highlighted in real-time!

> This is a blockquote to show formatting capabilities.

\`\`\`javascript
// Sample code block
function hello() {
  console.log("Hello, World!");
}
\`\`\`

[Link to documentation](https://example.com)
`;

  const handleSave = (content: string) => {
    setSavedContent(content);
    console.log('Content saved:', content);
  };

  const handleCancel = () => {
    console.log('Edit cancelled');
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Enhanced LiveDiffEditor Test</h1>
        <p className="text-gray-600">
          Testing the improved LiveDiffEditor with proper markdown rendering, dedicated edit button, and enhanced split view.
        </p>
      </div>

      <div className="space-y-4 mb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">🚀 Enhanced Features:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>Proper Markdown Rendering:</strong> Content displays as formatted markdown by default</li>
            <li>• <strong>Dedicated Edit Button:</strong> No more click-anywhere-to-edit - explicit edit button for better UX</li>
            <li>• <strong>Split View Mode:</strong> Side-by-side editor and live preview for rich text editing</li>
            <li>• <strong>Consistent Package Usage:</strong> Uses @uiw/react-md-editor for both editing and preview</li>
            <li>• <strong>Loading States:</strong> Smooth transitions with loading indicators</li>
            <li>• <strong>Improved Accessibility:</strong> Better keyboard navigation and visual indicators</li>
          </ul>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">🧪 Test Instructions:</h3>
          <ol className="text-sm text-green-700 space-y-1">
            <li>1. <strong>View Mode:</strong> Notice how markdown content is properly rendered by default</li>
            <li>2. <strong>Edit Button:</strong> Hover over content to see the dedicated "Edit" button appear</li>
            <li>3. <strong>Standard Editing:</strong> Try the basic text editor with diff preview</li>
            <li>4. <strong>Rich Text Editing:</strong> Use the markdown editor with toolbar for formatting</li>
            <li>5. <strong>Split View:</strong> Experience side-by-side editing with live preview</li>
            <li>6. <strong>Save/Cancel:</strong> Test the improved save/cancel functionality with loading states</li>
          </ol>
        </div>
      </div>

      <div className="space-y-8">
        {/* Standard Text Editor */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Standard Text Editor</h2>
          <p className="text-sm text-gray-600">Basic text editing with diff preview</p>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue="This is a simple text editor example.\n\nYou can edit this content and see the changes highlighted."
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your content here..."
              className="min-h-[200px]"
            />
          </div>
        </div>

        {/* Rich Text Editor (Single View) */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Rich Text Editor (Single View)</h2>
          <p className="text-sm text-gray-600">Markdown editing with rich text toolbar</p>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your markdown content here..."
              className="min-h-[200px]"
              enableRichText={true}
            />
          </div>
        </div>

        {/* Rich Text Editor with Split View */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Rich Text Editor with Split View</h2>
          <p className="text-sm text-gray-600">Side-by-side editing with live preview</p>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              onSave={handleSave}
              onCancel={handleCancel}
              placeholder="Enter your markdown content here..."
              className="min-h-[200px]"
              enableRichText={true}
              splitView={true}
            />
          </div>
        </div>

        {/* Read-only Display */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Read-only Display</h2>
          <p className="text-sm text-gray-600">Non-editable mode with proper markdown rendering</p>
          <div className="border rounded-lg p-4 bg-white">
            <LiveDiffEditor
              initialValue={initialContent}
              editable={false}
              enableRichText={true}
              className="min-h-[200px]"
            />
          </div>
        </div>

        {/* Saved Content Display */}
        {savedContent && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Last Saved Content (Markdown)</h2>
            <div className="border rounded-lg p-4 bg-gray-50">
              <pre className="whitespace-pre-wrap text-sm">{savedContent}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestDiffEditorPage;
