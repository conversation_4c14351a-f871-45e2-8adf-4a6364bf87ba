import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { ConnectionStatus } from "@/services/websocketService"

interface WebSocketState {
  connectionStatus: ConnectionStatus
  error: string | null
  isReconnecting: boolean
  lastConnectedAt: string | null
  reconnectAttempts: number
}

const initialState: WebSocketState = {
  connectionStatus: 'disconnected',
  error: null,
  isReconnecting: false,
  lastConnectedAt: null,
  reconnectAttempts: 0,
}

const websocketSlice = createSlice({
  name: "websocket",
  initialState,
  reducers: {
    setConnectionStatus(state, action: PayloadAction<ConnectionStatus>) {
      const newStatus = action.payload
      const previousStatus = state.connectionStatus
      
      state.connectionStatus = newStatus
      
      // Clear error when successfully connected
      if (newStatus === 'connected') {
        state.error = null
        state.isReconnecting = false
        state.lastConnectedAt = new Date().toISOString()
        state.reconnectAttempts = 0
      }
      
      // Set reconnecting flag when transitioning from error/disconnected to connecting
      if (newStatus === 'connecting' && (previousStatus === 'error' || previousStatus === 'disconnected')) {
        state.isReconnecting = previousStatus === 'error' || state.reconnectAttempts > 0
      }
      
      // Clear reconnecting flag when not connecting
      if (newStatus !== 'connecting') {
        state.isReconnecting = false
      }
    },
    
    setConnectionError(state, action: PayloadAction<string>) {
      state.error = action.payload
      state.connectionStatus = 'error'
      state.isReconnecting = false
    },
    
    incrementReconnectAttempts(state) {
      state.reconnectAttempts += 1
      state.isReconnecting = true
    },
    
    resetReconnectAttempts(state) {
      state.reconnectAttempts = 0
      state.isReconnecting = false
    },
    
    clearError(state) {
      state.error = null
    },
    
    // Action to trigger WebSocket connection
    connectWebSocket() {
      // This action will be handled by middleware
    },
    
    // Action to trigger WebSocket disconnection
    disconnectWebSocket() {
      // This action will be handled by middleware
    },
    
    // Action to send message through WebSocket
    sendWebSocketMessage(state, action: PayloadAction<{
      query: string;
      queryId: string;
      sources: {
        web: boolean;
        database: boolean;
        unstructured: boolean;
        feedback: boolean;
      };
    }>) {
      // This action will be handled by middleware
      // The payload contains the message data to send
    },
  },
})

export const {
  setConnectionStatus,
  setConnectionError,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  clearError,
  connectWebSocket,
  disconnectWebSocket,
  sendWebSocketMessage,
} = websocketSlice.actions

export default websocketSlice.reducer

// Selectors
export const selectWebSocketState = (state: { websocket: WebSocketState }) => state.websocket
export const selectConnectionStatus = (state: { websocket: WebSocketState }) => state.websocket.connectionStatus
export const selectIsConnected = (state: { websocket: WebSocketState }) => state.websocket.connectionStatus === 'connected'
export const selectConnectionError = (state: { websocket: WebSocketState }) => state.websocket.error
export const selectIsReconnecting = (state: { websocket: WebSocketState }) => state.websocket.isReconnecting
