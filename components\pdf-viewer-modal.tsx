"use client"

import React, { useState, useCallback, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/TextLayer.css';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  Download,
  FileText,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Configure PDF.js worker for Next.js
// Use a reliable CDN that supports the correct version
if (typeof window !== 'undefined') {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`
}

interface PDFViewerModalProps {
  isOpen: boolean
  onClose: () => void
  pdfPath: string
  pdfName: string
  initialPage?: number
  pageRange?: {
    start: number
    end: number
  }
}

export function PDFViewerModal({
  isOpen,
  onClose,
  pdfPath,
  pdfName,
  initialPage = 1,
  pageRange
}: PDFViewerModalProps) {
  const [numPages, setNumPages] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(initialPage)
  const [scale, setScale] = useState<number>(1.2)
  const [rotation, setRotation] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState<number>(0)

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully:', { numPages, pdfPath })
    setNumPages(numPages)
    setError(null)

    // If we have a page range, start at the beginning of the range
    if (pageRange?.start) {
      setCurrentPage(Math.max(1, Math.min(pageRange.start, numPages)))
    } else if (initialPage) {
      setCurrentPage(Math.max(1, Math.min(initialPage, numPages)))
    }
  }, [initialPage, pageRange, pdfPath])

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('PDF load error:', { error, pdfPath })

    // Provide more specific error messages based on the error type
    let errorMessage = 'Failed to load PDF document'
    if (error.message.includes('404') || error.message.includes('Not Found')) {
      errorMessage = `PDF file not found: ${pdfName}. Please check if the file exists in the /public/pdfs/ directory.`
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      errorMessage = 'Network error. Please check your connection and try again.'
    } else if (error.message.includes('Invalid PDF')) {
      errorMessage = 'Invalid PDF file. The document may be corrupted.'
    } else if (error.message.includes('Missing PDF')) {
      errorMessage = `PDF file "${pdfName}" could not be loaded. Please verify the file exists.`
    }

    setError(errorMessage)
    setLoading(false)
  }, [pdfPath, pdfName])

  const goToPreviousPage = useCallback(() => {
    setCurrentPage(prev => Math.max(1, prev - 1))
  }, [])

  const goToNextPage = useCallback(() => {
    setCurrentPage(prev => Math.min(numPages, prev + 1))
  }, [numPages])

  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(3, prev + 0.2))
  }, [])

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(0.5, prev - 0.2))
  }, [])

  const rotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360)
  }, [])

  const resetView = useCallback(() => {
    setScale(1.2)
    setRotation(0)
  }, [])

  const retryLoadPDF = useCallback(() => {
    setLoading(true)
    setError(null)
    setRetryCount(prev => prev + 1)
  }, [])

  const downloadPDF = useCallback(() => {
    const link = document.createElement('a')
    link.href = pdfPath
    link.download = pdfName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [pdfPath, pdfName])

  // Reset state when modal opens or PDF changes
  useEffect(() => {
    if (isOpen && pdfPath) {
      console.log('PDF Modal opened with:', { pdfPath, pdfName, initialPage, pageRange })
      setLoading(true)
      setError(null)
      setCurrentPage(initialPage)
      setScale(1.2)
      setRotation(0)
      setRetryCount(0)
    }
  }, [isOpen, pdfPath, pdfName, initialPage, pageRange])

  // Check if current page is within the highlighted range
  const isInRange = pageRange && currentPage >= pageRange.start && currentPage <= pageRange.end

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          goToPreviousPage()
          break
        case 'ArrowRight':
          event.preventDefault()
          goToNextPage()
          break
        case '=':
        case '+':
          event.preventDefault()
          zoomIn()
          break
        case '-':
          event.preventDefault()
          zoomOut()
          break
        case 'r':
        case 'R':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            rotate()
          }
          break
        case '0':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            resetView()
          }
          break
        case 'Escape':
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, goToPreviousPage, goToNextPage, zoomIn, zoomOut, rotate, resetView, onClose])

  return (
    <TooltipProvider>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-7xl w-[95vw] h-[95vh] flex flex-col p-0 gap-0">
        <DialogHeader className="px-4 sm:px-6 py-3 sm:py-4 border-b bg-white">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 flex-shrink-0" />
              <DialogTitle className="text-sm sm:text-lg font-semibold truncate">
                {pdfName}
              </DialogTitle>
              {pageRange && (
                <Badge
                  variant={isInRange ? "default" : "outline"}
                  className={cn(
                    "text-xs hidden sm:inline-flex",
                    isInRange && "bg-blue-100 text-blue-700 border-blue-200"
                  )}
                >
                  Pages {pageRange.start}-{pageRange.end}
                </Badge>
              )}
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadPDF}
                  className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3"
                >
                  <Download className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Download</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download PDF file</p>
              </TooltipContent>
            </Tooltip>
          </div>
          {/* Mobile page range badge */}
          {pageRange && (
            <div className="sm:hidden mt-2">
              <Badge
                variant={isInRange ? "default" : "outline"}
                className={cn(
                  "text-xs",
                  isInRange && "bg-blue-100 text-blue-700 border-blue-200"
                )}
              >
                Pages {pageRange.start}-{pageRange.end}
              </Badge>
            </div>
          )}
        </DialogHeader>

        {/* Toolbar */}
        <div className="px-4 sm:px-6 py-2 sm:py-3 border-b bg-gray-50 flex flex-col sm:flex-row items-center gap-3 sm:justify-between">
          {/* Page Navigation */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousPage}
              disabled={currentPage <= 1}
              className="px-2 sm:px-3"
            >
              <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>

            <span className="text-xs sm:text-sm font-medium px-2 sm:px-3 whitespace-nowrap">
              Page {currentPage} of {numPages}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={currentPage >= numPages}
              className="px-2 sm:px-3"
            >
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
          </div>

          {/* Zoom and Controls */}
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={zoomOut}
              disabled={scale <= 0.5}
              className="px-2 sm:px-3"
            >
              <ZoomOut className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>

            <span className="text-xs sm:text-sm font-medium px-1 sm:px-2 whitespace-nowrap">
              {Math.round(scale * 100)}%
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={zoomIn}
              disabled={scale >= 3}
              className="px-2 sm:px-3"
            >
              <ZoomIn className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={rotate}
              className="px-2 sm:px-3"
            >
              <RotateCw className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={resetView}
              className="px-2 sm:px-3 text-xs sm:text-sm"
            >
              <span className="hidden sm:inline">Reset</span>
              <span className="sm:hidden">↻</span>
            </Button>
          </div>
        </div>

        {/* PDF Content */}
        <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-2 sm:p-4">
          {error && (
            <div className="flex flex-col items-center gap-3 text-red-600">
              <AlertCircle className="h-8 w-8" />
              <div className="text-center">
                <p className="font-medium">Error loading PDF</p>
                <p className="text-sm text-gray-600 mt-1">{error}</p>
                <div className="flex gap-2 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={retryLoadPDF}
                  >
                    Retry
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          {!error && (
            <Document
              key={`${pdfPath}-${retryCount}`}
              file={pdfPath}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
            >
              {loading && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Loading PDF...</span>
                </div>
              )}
              <Page
                pageNumber={currentPage}
                scale={scale}
                rotate={rotation}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                loading={
                  <div className="flex items-center gap-2 text-gray-600 p-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span>Loading page...</span>
                  </div>
                }
                className={cn(
                  "shadow-lg",
                  isInRange && "ring-2 ring-blue-400 ring-opacity-50"
                )}
              />
            </Document>
          )}
        </div>
      </DialogContent>
    </Dialog>
    </TooltipProvider>
  )
}
