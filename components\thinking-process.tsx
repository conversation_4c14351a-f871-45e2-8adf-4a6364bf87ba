import { useSelector, useDispatch } from "react-redux"
import type { RootState, AppDispatch } from "@/store"
import type { ThinkingStep as ThinkingStepType } from "@/types"
import { ThinkingStepComponent } from "./thinking-step"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { resetMessageThinkingSteps } from "@/store/queriesSlice"
import { sendWebSocketMessage } from "@/store/websocketSlice"
import { RefreshCw } from "lucide-react"
import { EmptyState } from "./empty-state"

interface ThinkingProcessProps {
  currentQuery: string
  steps?: ThinkingStepType[] // Make optional for backward compatibility
  messageId?: string // Add messageId to identify which message's thinking steps to show
}

export function ThinkingProcess({ currentQuery, steps, messageId }: ThinkingProcessProps) {
  const dispatch = useDispatch<AppDispatch>()

  // Get current query and message data
  const activeQueryId = useSelector((state: RootState) => state.queries.activeQueryId)
  const currentQuery_data = useSelector((state: RootState) =>
    state.queries.queries.find(q => q.id === activeQueryId)
  )
  const sourcesConfig = useSelector((state: RootState) => state.sources.sources)

  // Find the current message to get its thinking steps
  let currentMessage = null
  if (currentQuery_data && messageId) {
    currentMessage = currentQuery_data.messages.find(m => m.id === messageId)
  } else if (currentQuery_data) {
    // Fall back to last assistant message if no messageId provided
    currentMessage = [...currentQuery_data.messages].reverse().find(m => m.type === 'assistant')
  }

  // Get thinking steps and processing error from current message
  const websocketThinkingSteps = currentMessage?.thinkingSteps || {}
  const processingError = currentMessage?.processingError || null

  // Convert WebSocket thinking steps to ThinkingStep format
  const convertedSteps: ThinkingStepType[] = Object.entries(websocketThinkingSteps).map(([agentName, stepData]) => ({
    id: agentName,
    description: stepData.displayName,
    status: stepData.status === 'idle' ? 'pending' : stepData.status,
    // Store the result data in a structured way for the thinking step component
    details: stepData.result ? JSON.stringify(stepData.result, null, 2) : stepData.message,
    error: stepData.error
  }))

  // Use WebSocket steps if available, otherwise fall back to provided steps
  const displaySteps = convertedSteps.length > 0 ? convertedSteps : (steps || [])

  // Check if any step is in progress or has errors
  const hasInProgressSteps = displaySteps.some((step) => step.status === "in-progress")
  const hasErrorSteps = displaySteps.some((step) => step.status === "error")
  const hasCompletedSteps = displaySteps.some((step) => step.status === "completed")

  const handleRetry = () => {
    // Reset thinking steps for current message and retry the query
    if (activeQueryId && currentMessage) {
      dispatch(resetMessageThinkingSteps({
        queryId: activeQueryId,
        messageId: currentMessage.id
      }))
    }

    const sources = {
      web: sourcesConfig.linkedin.enabled,
      database: sourcesConfig.rossDatabase.enabled,
      unstructured: sourcesConfig.email.enabled,
      feedback: sourcesConfig.feedback.enabled,
    }

    if (activeQueryId) {
      dispatch(sendWebSocketMessage({
        query: currentQuery,
        queryId: activeQueryId,
        sources
      }))
    }
  }
  return (
    <div className="space-y-6">
      {/* Tab navigation with consistent styling */}
      <div className="flex items-center gap-8 pb-4 border-b border-gray-200">
        <Button
          variant="ghost"
          className="text-teal-500 font-semibold px-0 py-2 h-auto relative after:content-[''] after:absolute after:left-0 after:bottom-[-16px] after:h-[2px] after:w-full after:bg-teal-500"
        >
          Answer
        </Button>
        {/* <Button variant="ghost" disabled className="text-gray-500 font-semibold px-0 py-2 h-auto hover:text-gray-700">
          Catalog
        </Button> */}
      </div>

      {/* Thinking steps with sources panel styling */}
      <div className="bg-gray-50/20 rounded-xl border border-gray-200 p-6">
        {processingError ? (
          <div className="text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Processing Error:</p>
                <p className="text-sm mt-1">{processingError}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="ml-4"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </div>
          </div>
        ) : displaySteps.length === 0 ? (
          <EmptyState
            type="thinking"
            title="Ready to process your query"
            description="Send a message to start the AI thinking process."
          />
        ) : (
          <>
            <div className="space-y-4">
              {displaySteps.map((step, index) => (
                <ThinkingStepComponent key={step.id} step={step} isLastStep={index === displaySteps.length - 1} />
              ))}
            </div>

            {hasErrorSteps && !hasInProgressSteps && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <p className="text-base text-red-600">Some agents failed to process your request.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRetry}
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Retry Failed
                  </Button>
                </div>
              </div>
            )}

            {hasInProgressSteps && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-base text-gray-600 animate-pulse">Generating answer...</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
