{"name": "ross-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reduxjs/toolkit": "latest", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@types/markdown-it": "^14.1.2", "@types/turndown": "^5.0.5", "@uiw/react-md-editor": "^4.0.7", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "diff": "^8.0.2", "embla-carousel-react": "8.5.1", "framer-motion": "^12.19.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "markdown-it": "^14.1.0", "moment": "^2.30.1", "next": "15.2.4", "next-themes": "^0.4.4", "pdfjs-dist": "^5.3.31", "react": "^19", "react-day-picker": "8.10.1", "react-diff-view": "^3.3.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-markdown": "latest", "react-pdf": "^9.2.1", "react-redux": "latest", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redux": "latest", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "uuid": "latest", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/diff": "^8.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-pdf": "^7.0.0", "copy-webpack-plugin": "^13.0.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}, "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af"}