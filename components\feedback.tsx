import React, { useState } from "react";
import { ArrowU<PERSON>, Edit3, Eye } from "lucide-react";
import moment from "moment";
import { patchFeedbackApi, updateAiResponseApi } from "@/lib/api";
import LiveDiffEditor from "./LiveDiffEditor";

type QueryItem = {
  feedback: string[];
  created_at: string;
  urgency: string;
  source: string;
  question: string;
  ai_answer: string;
  tags: string[];
  _id: string;
};

interface Query {
  data: QueryItem;
}

function Feedback({
  data,
  setOpen,
}: {
  data: QueryItem;
  setOpen: (open: boolean) => void;
}) {
  console.log("Feedback data", data);
  const [tags, setTags] = useState(data.tags || []);
  const [feedback, setFeedback] = useState("");
  const [msg, setMsg] = useState("");
  const [aiResponse, setAiResponse] = useState(data.ai_answer || "");
  const [isEditingResponse, setIsEditingResponse] = useState(false);
  const [responseUpdateMsg, setResponseUpdateMsg] = useState("");

  const handleFeedbackSubmit = async () => {
    try {
      const res = await patchFeedbackApi(data._id, {
        question: data.question,
        feedback_text: feedback,
      });
      setMsg("Feedback submitted successfully");
      setFeedback("");
      setOpen(false); // Close the modal after submission
      window.location.reload();
    } catch (error) {
      console.error("Failed to submit feedback");
    }
  };

  const handleAiResponseSave = async (updatedResponse: string) => {
    try {
      console.log("Updating AI Response:", updatedResponse);

      // Call the API to update the AI response
      await updateAiResponseApi(data._id, { ai_answer: updatedResponse });

      // Update local state
      setAiResponse(updatedResponse);
      setResponseUpdateMsg("AI response updated successfully!");
      setIsEditingResponse(false);

      // Clear the success message after 3 seconds
      setTimeout(() => {
        setResponseUpdateMsg("");
      }, 3000);

    } catch (error) {
      console.error("Failed to update AI response:", error);
      setResponseUpdateMsg("Failed to update AI response. Please try again.");

      // Clear the error message after 5 seconds
      setTimeout(() => {
        setResponseUpdateMsg("");
      }, 5000);
    }
  };

  const handleAiResponseCancel = () => {
    setIsEditingResponse(false);
    setResponseUpdateMsg("");
  };
  return (
    <div className="py-5">
      <div className="flex gap-5 items-center">
        <p>
          {" "}
          <span className="font-semibold">Date:</span>{" "}
          {moment.utc(data.created_at).local().format("YYYY-MM-DD")}
        </p>
        <p>
          {" "}
          <span className="font-semibold">Source:</span> {data.source}
        </p>
      </div>
      <div className="flex flex-col gap-1 mt-4">
        <h1 className="text-[#3B4154] text-lg font-semibold">Query</h1>
        <p>{data.question}</p>
      </div>
      <div className="flex flex-col gap-1 mt-4">
        <div className="flex items-center justify-between">
          <h1 className="text-[#3B4154] text-lg font-semibold">AI Response</h1>
          <div className="flex items-center gap-2">
            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full flex items-center gap-1">
              <Edit3 size={12} />
              Rich Text Enabled
            </span>
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full flex items-center gap-1">
              <Eye size={12} />
              Split View Available
            </span>
          </div>
        </div>

        {responseUpdateMsg && (
          <div className={`text-sm p-2 rounded ${
            responseUpdateMsg.includes("successfully")
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}>
            {responseUpdateMsg}
          </div>
        )}

        <div className="border-2 border-blue-300 rounded-lg mt-2">
          <LiveDiffEditor
            initialValue={aiResponse || "No AI response available."}
            className="min-h-[200px]"
            placeholder="No AI response available."
            editable={true}
            enableRichText={true}
            splitView={true}
            onSave={handleAiResponseSave}
            onCancel={handleAiResponseCancel}
          />
        </div>

        <div className="text-xs text-gray-500 mt-2 flex items-center gap-4">
          <span>💡 Click to edit with rich text formatting</span>
          <span>🔄 Changes are saved automatically with markdown conversion</span>
          <span>👁️ Split view shows live preview while editing</span>
        </div>
      </div>
      <div className="flex gap-2 mt-4">
        <h2 className="font-medium text-md">Tags</h2>
        <div>
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-block bg-gray-100 text-gray-500 text-sm mr-2 px-2.5 py-0.5 rounded"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-2 mt-4">
        <div>
          <h1 className="font-medium text-md">Feedback</h1>
          <p className="text-sm text-gray-500">
            All feedback will be logged, preserved & utilized to fine-tune the
            AI's future replies
          </p>
        </div>
        <div className="flex justify-between items-center gap-2 mt-2 border border-gray-300 rounded-lg p-2">
          <input
            type="text"
            placeholder="Enter comment here..."
            className="outline-none"
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
          />
          <button onClick={handleFeedbackSubmit}>
            <ArrowUp className="bg-teal-500 text-white rounded-full p-1 w-8 h-8" />
          </button>
        </div>
        {msg && <p className="text-green-500 text-sm mt-2">{msg}</p>}

        <div className="flex flex-col gap-3 mt-4 mb-5">
          {data.feedback.length > 0 ? (
            data.feedback.map((text, index) => (
              <div key={index} className="flex gap-2 items-start">
                <div>
                  <p className="flex justify-center items-center text-white rounded-full p-1 w-8 h-8">
                    <img src="/ross-feedback-icon.png" alt="S" />
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <p>Ross Senior Sales Engineer</p>
                  <p key={index} className="text-md text-gray-500">
                    {text}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500">No feedback available.</p>
          )}
        </div>
      </div>
    </div>
  );
}

export default Feedback;
