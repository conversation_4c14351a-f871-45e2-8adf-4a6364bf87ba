import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import type { FC } from "react";

interface MarkdownMessageProps {
  content: string;
}

export const MarkdownMessage: FC<MarkdownMessageProps> = ({ content }) => {
  return (
    <div className="max-w-none text-gray-700">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          code({node, inline, className, children, ...props}: any) {
            return !inline ? (
              <pre className="bg-gray-300 text-black rounded-md p-3 overflow-x-auto my-3">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="bg-gray-200 text-red-700 rounded px-1">{children}</code>
            );
          },
          h1({children}) {
            return (
              <h1 className="text-2xl font-bold text-gray-900 mt-6 mb-4 pb-2 border-b border-gray-300">
                {children}
              </h1>
            );
          },
          h2({children}) {
            return (
              <h2 className="text-xl font-semibold text-gray-900 mt-5 mb-3 pb-1 border-b border-gray-200">
                {children}
              </h2>
            );
          },
          h3({children}) {
            return (
              <h3 className="text-lg font-semibold text-gray-900 mt-4 mb-2">
                {children}
              </h3>
            );
          },
          h4({children}) {
            return (
              <h4 className="text-base font-semibold text-gray-900 mt-3 mb-2">
                {children}
              </h4>
            );
          },
          h5({children}) {
            return (
              <h5 className="text-sm font-semibold text-gray-900 mt-3 mb-1">
                {children}
              </h5>
            );
          },
          h6({children}) {
            return (
              <h6 className="text-sm font-medium text-gray-700 mt-2 mb-1">
                {children}
              </h6>
            );
          },
          table({children}) {
            return (
              <table className="border border-gray-300 my-4 shadow-sm">{children}</table>
            );
          },
          th({children}) {
            return (
              <th className="bg-gray-100 border border-gray-300 px-2 py-1 font-semibold">{children}</th>
            );
          },
          td({children}) {
            return (
              <td className="border border-gray-300 px-2 py-1">{children}</td>
            );
          },
          blockquote({children}) {
            return (
              <blockquote className="border-l-4 border-blue-400 bg-blue-50 italic pl-4 pr-2 py-2 my-4">
                {children}
              </blockquote>
            );
          },
          ul({children}) {
            return (
              <ul className="list-disc list-outside my-3 space-y-1 ml-6 pl-2">
                {children}
              </ul>
            );
          },
          ol({children}) {
            return (
              <ol className="list-decimal list-outside my-3 space-y-1 ml-6 pl-2">
                {children}
              </ol>
            );
          },
          li({children}) {
            return (
              <li className="text-gray-700 leading-relaxed pl-1">
                {children}
              </li>
            );
          },
          p({children}) {
            return (
              <p className="my-3 leading-relaxed text-gray-700">
                {children}
              </p>
            );
          },
          strong({children}) {
            return (
              <strong className="font-semibold text-gray-900">
                {children}
              </strong>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};