import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { diffWords, Change } from 'diff';

interface LiveDiffEditorProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onCancel?: () => void;
  className?: string;
  placeholder?: string;
  editable?: boolean;
}

interface DiffSegment {
  value: string;
  added?: boolean;
  removed?: boolean;
}

// Simple debounce function to avoid lodash dependency
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  const debouncedFunc = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };

  debouncedFunc.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFunc;
};

const LiveDiffEditor: React.FC<LiveDiffEditorProps> = ({
  initialValue = '',
  onSave,
  onCancel,
  className = '',
  placeholder = 'Enter text...',
  editable = true,
}) => {
  const [currentValue, setCurrentValue] = useState(initialValue);
  const [isEditing, setIsEditing] = useState(false);

  // Debounced diff calculation for performance
  const debouncedDiffCalculation = useMemo(
    () => debounce((original: string, current: string) => {
      return diffWords(original, current);
    }, 100),
    []
  );

  // Calculate diff between original and current text
  const diffSegments = useMemo(() => {
    if (!isEditing || currentValue === initialValue) {
      return [{ value: currentValue || '' }];
    }

    // Handle empty strings and very large texts
    if (!initialValue && !currentValue) {
      return [{ value: '' }];
    }

    // For very large texts (>10000 chars), use line-based diff for performance
    if (initialValue.length > 10000 || currentValue.length > 10000) {
      const lines1 = initialValue.split('\n');
      const lines2 = currentValue.split('\n');
      // For now, fall back to simple display for very large texts
      return [{ value: currentValue }];
    }

    try {
      const diff = diffWords(initialValue, currentValue);
      return diff.length > 0 ? diff : [{ value: currentValue }];
    } catch (error) {
      console.warn('Diff calculation failed:', error);
      return [{ value: currentValue }];
    }
  }, [initialValue, currentValue, isEditing]);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentValue(e.target.value);
  }, []);

  const handleStartEditing = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleSave = useCallback(() => {
    setIsEditing(false);
    onSave?.(currentValue);
  }, [currentValue, onSave]);

  const handleCancel = useCallback(() => {
    setCurrentValue(initialValue);
    setIsEditing(false);
    onCancel?.();
  }, [initialValue, onCancel]);

  const renderDiffSegment = (segment: DiffSegment, index: number) => {
    // Handle empty segments
    if (!segment.value) {
      return null;
    }

    // Escape HTML and handle special characters
    const safeValue = segment.value;

    if (segment.added) {
      return (
        <span
          key={index}
          className="bg-green-100 text-green-800 px-1 rounded"
          style={{ backgroundColor: '#dcfce7' }}
          title="Added text"
        >
          {safeValue}
        </span>
      );
    }

    if (segment.removed) {
      return (
        <span
          key={index}
          className="bg-red-100 text-red-800 px-1 rounded line-through"
          style={{ backgroundColor: '#fee2e2', textDecoration: 'line-through' }}
          title="Removed text"
        >
          {safeValue}
        </span>
      );
    }

    return <span key={index}>{safeValue}</span>;
  };

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      if (debouncedDiffCalculation && typeof debouncedDiffCalculation.cancel === 'function') {
        debouncedDiffCalculation.cancel();
      }
    };
  }, [debouncedDiffCalculation]);

  if (!editable) {
    return (
      <div className={`p-3 ${className}`}>
        {diffSegments.map(renderDiffSegment)}
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {!isEditing ? (
        <div
          className="p-3 cursor-pointer hover:bg-gray-50 rounded border-2 border-transparent hover:border-gray-200 transition-colors"
          onClick={handleStartEditing}
        >
          {currentValue || <span className="text-gray-400">{placeholder}</span>}
        </div>
      ) : (
        <div className="space-y-3">
          {/* Live diff preview */}
          <div className="p-3 bg-gray-50 rounded border min-h-[100px] text-sm">
            <div className="text-xs text-gray-500 mb-2 font-medium">Live Preview:</div>
            <div className="whitespace-pre-wrap">
              {diffSegments.map(renderDiffSegment)}
            </div>
          </div>
          
          {/* Editable textarea */}
          <textarea
            value={currentValue}
            onChange={handleTextChange}
            className="w-full p-3 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={6}
            placeholder={placeholder}
            autoFocus
          />
          
          {/* Action buttons */}
          <div className="flex gap-2 justify-end m">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm text-black bg-[#b2d8d8] rounded hover:bg-green-100 transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveDiffEditor;
