import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { diffWords, Change } from 'diff';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import TurndownService from 'turndown';
import MarkdownIt from 'markdown-it';

// Dynamically import the markdown editor to avoid SSR issues
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default),
  { ssr: false }
);

interface LiveDiffEditorProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onCancel?: () => void;
  className?: string;
  placeholder?: string;
  editable?: boolean;
  enableRichText?: boolean; // New prop to enable rich text editing
  splitView?: boolean; // New prop to enable split view layout
}

interface DiffSegment {
  value: string;
  added?: boolean;
  removed?: boolean;
}

// Simple debounce function to avoid lodash dependency
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  const debouncedFunc = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };

  debouncedFunc.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFunc;
};

// Initialize markdown conversion utilities
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
});

const markdownIt = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
});

const LiveDiffEditor: React.FC<LiveDiffEditorProps> = ({
  initialValue = '',
  onSave,
  onCancel,
  className = '',
  placeholder = 'Enter text...',
  editable = true,
  enableRichText = false,
  splitView = false,
}) => {
  const [currentValue, setCurrentValue] = useState(initialValue);
  const [isEditing, setIsEditing] = useState(false);
  const [richTextValue, setRichTextValue] = useState(initialValue);
  const [showSplitView, setShowSplitView] = useState(false);

  // Debounced diff calculation for performance
  const debouncedDiffCalculation = useMemo(
    () => debounce((original: string, current: string) => {
      return diffWords(original, current);
    }, 100),
    []
  );

  // Calculate diff between original and current text
  const diffSegments = useMemo(() => {
    if (!isEditing || currentValue === initialValue) {
      return [{ value: currentValue || '' }];
    }

    // Handle empty strings and very large texts
    if (!initialValue && !currentValue) {
      return [{ value: '' }];
    }

    // For very large texts (>10000 chars), use line-based diff for performance
    if (initialValue.length > 10000 || currentValue.length > 10000) {
      const lines1 = initialValue.split('\n');
      const lines2 = currentValue.split('\n');
      // For now, fall back to simple display for very large texts
      return [{ value: currentValue }];
    }

    try {
      const diff = diffWords(initialValue, currentValue);
      return diff.length > 0 ? diff : [{ value: currentValue }];
    } catch (error) {
      console.warn('Diff calculation failed:', error);
      return [{ value: currentValue }];
    }
  }, [initialValue, currentValue, isEditing]);

  // Markdown conversion functions
  const convertToMarkdown = useCallback((htmlContent: string) => {
    try {
      return turndownService.turndown(htmlContent);
    } catch (error) {
      console.warn('Failed to convert to markdown:', error);
      return htmlContent;
    }
  }, []);

  const convertFromMarkdown = useCallback((markdownContent: string) => {
    try {
      return markdownIt.render(markdownContent);
    } catch (error) {
      console.warn('Failed to convert from markdown:', error);
      return markdownContent;
    }
  }, []);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentValue(e.target.value);
  }, []);

  const handleRichTextChange = useCallback((value?: string) => {
    if (value !== undefined) {
      setRichTextValue(value);
      // Convert rich text to plain text for diff comparison
      const plainText = convertToMarkdown(value);
      setCurrentValue(plainText);
    }
  }, [convertToMarkdown]);

  const handleStartEditing = useCallback(() => {
    setIsEditing(true);
    if (enableRichText && splitView) {
      setShowSplitView(true);
    }
  }, [enableRichText, splitView]);

  const handleSave = useCallback(() => {
    setIsEditing(false);
    setShowSplitView(false);

    // Log the markdown output to console
    const finalValue = enableRichText ? convertToMarkdown(richTextValue) : currentValue;
    console.log('Saved markdown content:', finalValue);

    onSave?.(finalValue);
  }, [currentValue, richTextValue, enableRichText, convertToMarkdown, onSave]);

  const handleCancel = useCallback(() => {
    setCurrentValue(initialValue);
    setRichTextValue(initialValue);
    setIsEditing(false);
    setShowSplitView(false);
    onCancel?.();
  }, [initialValue, onCancel]);

  const renderDiffSegment = (segment: DiffSegment, index: number) => {
    // Handle empty segments
    if (!segment.value) {
      return null;
    }

    // Escape HTML and handle special characters
    const safeValue = segment.value;

    if (segment.added) {
      return (
        <span
          key={index}
          className="bg-green-100 text-green-800 px-1 rounded"
          style={{ backgroundColor: '#dcfce7' }}
          title="Added text"
        >
          {safeValue}
        </span>
      );
    }

    if (segment.removed) {
      return (
        <span
          key={index}
          className="bg-red-100 text-red-800 px-1 rounded line-through"
          style={{ backgroundColor: '#fee2e2', textDecoration: 'line-through' }}
          title="Removed text"
        >
          {safeValue}
        </span>
      );
    }

    return <span key={index}>{safeValue}</span>;
  };

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      if (debouncedDiffCalculation && typeof debouncedDiffCalculation.cancel === 'function') {
        debouncedDiffCalculation.cancel();
      }
    };
  }, [debouncedDiffCalculation]);

  if (!editable) {
    return (
      <div className={`p-3 ${className}`}>
        {diffSegments.map(renderDiffSegment)}
      </div>
    );
  }

  // Enhanced render with rich text and split view support
  return (
    <div className={`relative ${className}`}>
      {!isEditing ? (
        <div
          className="p-3 cursor-pointer hover:bg-gray-50 rounded border-2 border-transparent hover:border-gray-200 transition-colors"
          onClick={handleStartEditing}
        >
          {currentValue || <span className="text-gray-400">{placeholder}</span>}
          {enableRichText && (
            <div className="mt-2 text-xs text-blue-600">
              Click to edit with rich text formatting
            </div>
          )}
        </div>
      ) : enableRichText && showSplitView ? (
        // Split view layout for rich text editing
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 50 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4 min-h-[400px]"
          >
            {/* Rich Text Editor */}
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">Rich Text Editor</div>
              <div className="border rounded-lg overflow-hidden">
                <MDEditor
                  value={richTextValue}
                  onChange={handleRichTextChange}
                  preview="edit"
                  hideToolbar={false}
                  height={350}
                  data-color-mode="light"
                />
              </div>
            </div>

            {/* Live Diff Preview */}
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">Live Preview</div>
              <div className="p-3 bg-gray-50 rounded-lg border min-h-[350px] text-sm overflow-auto">
                <div className="whitespace-pre-wrap">
                  {diffSegments.map(renderDiffSegment)}
                </div>
              </div>
            </div>

            {/* Action buttons for split view */}
            <div className="lg:col-span-2 flex gap-2 justify-end">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm text-black bg-red-100 rounded hover:bg-red-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 text-sm text-white bg-teal-500 rounded hover:bg-teal-600 transition-colors"
              >
                Save
              </button>
            </div>
          </motion.div>
        </AnimatePresence>
      ) : (
        // Standard editing layout
        <div className="space-y-3">
          {/* Live diff preview */}
          <div className="p-3 bg-gray-50 rounded border min-h-[100px] text-sm">
            <div className="text-xs text-gray-500 mb-2 font-medium">Live Preview:</div>
            <div className="whitespace-pre-wrap">
              {diffSegments.map(renderDiffSegment)}
            </div>
          </div>

          {/* Editable textarea or rich text editor */}
          {enableRichText ? (
            <div className="border rounded-lg overflow-hidden">
              <MDEditor
                value={richTextValue}
                onChange={handleRichTextChange}
                preview="edit"
                hideToolbar={false}
                height={200}
                data-color-mode="light"
              />
            </div>
          ) : (
            <textarea
              value={currentValue}
              onChange={handleTextChange}
              className="w-full p-3 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={6}
              placeholder={placeholder}
              autoFocus
            />
          )}

          {/* Action buttons */}
          <div className="flex gap-2 justify-end">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-black bg-red-100 rounded hover:bg-red-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm text-white bg-teal-500 rounded hover:bg-teal-600 transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveDiffEditor;
