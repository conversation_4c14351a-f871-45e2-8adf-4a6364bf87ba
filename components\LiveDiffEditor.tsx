import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { diffWords, Change } from 'diff';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import { Edit3, Eye, Save, X, Loader2 } from 'lucide-react';
import TurndownService from 'turndown';

// Dynamically import the markdown editor to avoid SSR issues
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default),
  { ssr: false }
);

// Dynamically import the markdown preview component
const MDPreview = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default.Markdown),
  { ssr: false }
);

interface LiveDiffEditorProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onCancel?: () => void;
  className?: string;
  placeholder?: string;
  editable?: boolean;
  enableRichText?: boolean; // New prop to enable rich text editing
  splitView?: boolean; // New prop to enable split view layout
}

interface DiffSegment {
  value: string;
  added?: boolean;
  removed?: boolean;
}

// Simple debounce function to avoid lodash dependency
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  const debouncedFunc = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };

  debouncedFunc.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFunc;
};

// Initialize markdown conversion utilities
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
});

const LiveDiffEditor: React.FC<LiveDiffEditorProps> = ({
  initialValue = '',
  onSave,
  onCancel,
  className = '',
  placeholder = 'Enter text...',
  editable = true,
  enableRichText = false,
  splitView = false,
}) => {
  const [currentValue, setCurrentValue] = useState(initialValue);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [editValue, setEditValue] = useState(initialValue);

  // Calculate diff between original and current text for preview
  const diffSegments = useMemo(() => {
    if (!isEditing || editValue === initialValue) {
      return [{ value: editValue || '' }];
    }

    // Handle empty strings
    if (!initialValue && !editValue) {
      return [{ value: '' }];
    }

    try {
      const diff = diffWords(initialValue, editValue);
      return diff.length > 0 ? diff : [{ value: editValue }];
    } catch (error) {
      console.warn('Diff calculation failed:', error);
      return [{ value: editValue }];
    }
  }, [initialValue, editValue, isEditing]);

  // Update current value when initial value changes
  useEffect(() => {
    setCurrentValue(initialValue);
    setEditValue(initialValue);
  }, [initialValue]);

  const handleStartEditing = useCallback(() => {
    setIsLoading(true);
    setEditValue(currentValue);

    // Simulate loading for smooth transition
    setTimeout(() => {
      setIsEditing(true);
      setIsLoading(false);
    }, 150);
  }, [currentValue]);

  const handleSave = useCallback(async () => {
    setIsLoading(true);

    try {
      // Log the markdown output to console
      console.log('Saved markdown content:', editValue);

      // Update current value
      setCurrentValue(editValue);

      // Call onSave callback
      await onSave?.(editValue);

      // Exit edit mode
      setIsEditing(false);
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [editValue, onSave]);

  const handleCancel = useCallback(() => {
    setEditValue(currentValue);
    setIsEditing(false);
    onCancel?.();
  }, [currentValue, onCancel]);

  const handleEditorChange = useCallback((value?: string) => {
    if (value !== undefined) {
      setEditValue(value);
    }
  }, []);

  const renderDiffSegment = (segment: DiffSegment, index: number) => {
    // Handle empty segments
    if (!segment.value) {
      return null;
    }

    const safeValue = segment.value;

    if (segment.added) {
      return (
        <span
          key={index}
          className="bg-green-100 text-green-800 px-1 rounded"
          style={{ backgroundColor: '#dcfce7' }}
          title="Added text"
        >
          {safeValue}
        </span>
      );
    }

    if (segment.removed) {
      return (
        <span
          key={index}
          className="bg-red-100 text-red-800 px-1 rounded line-through"
          style={{ backgroundColor: '#fee2e2', textDecoration: 'line-through' }}
          title="Removed text"
        >
          {safeValue}
        </span>
      );
    }

    return <span key={index}>{safeValue}</span>;
  };

  // Non-editable mode - just show markdown preview
  if (!editable) {
    return (
      <div className={`${className}`}>
        {enableRichText && currentValue ? (
          <MDPreview source={currentValue} />
        ) : (
          <div className="p-3 text-gray-700 whitespace-pre-wrap">
            {currentValue || placeholder}
          </div>
        )}
      </div>
    );
  }

  // Main render with improved UX
  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10 rounded-lg">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
        </div>
      )}

      {!isEditing ? (
        // Default display mode with proper markdown rendering
        <div className="space-y-3">
          <div className="relative group">
            {enableRichText && currentValue ? (
              <div className="prose prose-sm max-w-none">
                <MDPreview source={currentValue} />
              </div>
            ) : (
              <div className="p-3 text-gray-700 whitespace-pre-wrap min-h-[60px] border rounded-lg bg-gray-50">
                {currentValue || <span className="text-gray-400">{placeholder}</span>}
              </div>
            )}

            {/* Dedicated Edit Button */}
            <button
              onClick={handleStartEditing}
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1.5 shadow-sm"
              disabled={isLoading}
            >
              <Edit3 size={14} />
              Edit
            </button>
          </div>

          {enableRichText && (
            <div className="text-xs text-gray-500 flex items-center gap-2">
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full">Rich Text Enabled</span>
              {splitView && <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Split View Available</span>}
            </div>
          )}
        </div>
      ) : enableRichText && splitView ? (
        // Split view layout for rich text editing
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Edit Content</h3>
              <div className="flex items-center gap-2">
                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full flex items-center gap-1">
                  <Edit3 size={12} />
                  Rich Text Mode
                </span>
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full flex items-center gap-1">
                  <Eye size={12} />
                  Split View
                </span>
              </div>
            </div>

            {/* Split View Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[500px]">
              {/* Left: Rich Text Editor */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Edit3 size={16} className="text-blue-500" />
                  <span className="text-sm font-medium text-gray-700">Editor</span>
                </div>
                <div className="border rounded-lg overflow-hidden shadow-sm">
                  <MDEditor
                    value={editValue}
                    onChange={handleEditorChange}
                    preview="edit"
                    hideToolbar={false}
                    height={450}
                    data-color-mode="light"
                  />
                </div>
              </div>

              {/* Right: Live Preview */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Eye size={16} className="text-green-500" />
                  <span className="text-sm font-medium text-gray-700">Live Preview</span>
                </div>
                <div className="border rounded-lg overflow-hidden shadow-sm bg-white">
                  <div className="h-[450px] overflow-auto">
                    <div className="p-4">
                      <MDPreview source={editValue || placeholder} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex gap-3 justify-end pt-4 border-t">
              <button
                onClick={handleCancel}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <X size={16} />
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isLoading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
                {isLoading ? 'Saving...' : 'Save'}
              </button>
            </div>
          </motion.div>
        </AnimatePresence>
      ) : (
        // Standard editing layout (non-split view)
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Edit Content</h3>
              <div className="flex items-center gap-2">
                {enableRichText && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full flex items-center gap-1">
                    <Edit3 size={12} />
                    Rich Text Mode
                  </span>
                )}
              </div>
            </div>

            {/* Editor */}
            {enableRichText ? (
              <div className="border rounded-lg overflow-hidden shadow-sm">
                <MDEditor
                  value={editValue}
                  onChange={handleEditorChange}
                  preview="edit"
                  hideToolbar={false}
                  height={300}
                  data-color-mode="light"
                />
              </div>
            ) : (
              <textarea
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                rows={8}
                placeholder={placeholder}
                autoFocus
              />
            )}

            {/* Live diff preview for non-rich text mode */}
            {!enableRichText && editValue !== initialValue && (
              <div className="p-3 bg-gray-50 rounded-lg border text-sm">
                <div className="text-xs text-gray-500 mb-2 font-medium flex items-center gap-1">
                  <Eye size={12} />
                  Changes Preview:
                </div>
                <div className="whitespace-pre-wrap">
                  {diffSegments.map(renderDiffSegment)}
                </div>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex gap-3 justify-end pt-4 border-t">
              <button
                onClick={handleCancel}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <X size={16} />
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isLoading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
                {isLoading ? 'Saving...' : 'Save'}
              </button>
            </div>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
};

export default LiveDiffEditor;
