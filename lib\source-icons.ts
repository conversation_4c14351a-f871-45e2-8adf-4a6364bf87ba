import { Database, Linkedin, Mail, MessageSquare } from "lucide-react"
import type { SourceInfo } from "@/types"
import { SutraLogo } from "@/components/ui/sutra-logo"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/ross-logo"

// Icon mapping to avoid storing React components in Redux state
export const getSourceIcon = (sourceId: string) => {
  const iconMap = {
    rossDatabase: RossLogo,
    rossFiles: RossLogo,
    sutraWebSearch: SutraLogo,
    sutraFeedback: SutraLogo,
  }

  return iconMap[sourceId as keyof typeof iconMap] || Database
}

// Icon mapping by source type for other components
export const getIconForSourceType = (type: SourceInfo["type"]) => {
  const typeIconMap = {
    database: Database,
    linkedin: Linkedin,
    email: Mail,
    web: Database, // Using Database as fallback
    pdf: Database,
    docs: Database,
    faq: Database,
    feedback: MessageSquare,
    sutra: SutraLogo,
    ross: RossLogo,
  }

  return typeIconMap[type] || Database
}
