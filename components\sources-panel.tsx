"use client"

import { useDispatch, useSelector } from "react-redux"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Link as LinkIcon } from "lucide-react"
import { toggleSource } from "@/store/sourcesSlice"
import { getSourceIcon } from "@/lib/source-icons"
import type { RootState, AppDispatch } from "@/store"

export function SourcesPanel() {
  const dispatch = useDispatch<AppDispatch>()
  const sourcesConfig = useSelector((state: RootState) => state.sources.sources)

  return (
    <div className="flex justify-start" >
      <div className="flex items-center gap-3 flex-shrink-0 my-2 mr-3">
        <LinkIcon className="h-5 w-5 text-blue-500" />
        <span className="font-medium text-gray-700">Select Sources for Search:</span>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 text-base">
        {Object.entries(sourcesConfig).map(([key, source]) => {
          const IconComponent = getSourceIcon(source.config.id)
          return (
            <div key={key} className="flex items-center space-x-3 p-2 rounded-md border border-gray-300">
              <IconComponent className={`h-5 w-5 ${source.config.colorClass || "text-gray-600"}`} />
              <Label htmlFor={source.config.id} className="cursor-pointer text-base font-medium text-gray-700">
                {source.config.name}
              </Label>
              <Switch
                id={source.config.id}
                checked={source.enabled}
                onCheckedChange={() => dispatch(toggleSource(key as keyof typeof sourcesConfig))}
              />
            </div>
          )
        })}
      </div>
    </div>
  )
}
