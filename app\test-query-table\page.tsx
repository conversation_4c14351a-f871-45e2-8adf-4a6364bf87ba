'use client';

import React from 'react';
import QueryTable from '@/components/query-table';

const TestQueryTablePage = () => {
  // Sample data with various types of AI responses to test the enhanced functionality
  const sampleData = [
    {
      _id: "1",
      created_at: "2024-12-24T10:30:00Z",
      urgency: "high",
      source: "Website",
      question: "What are the best practices for valve maintenance in high-pressure systems?",
      ai_answer: `# Valve Maintenance Best Practices

For **high-pressure systems**, proper valve maintenance is crucial for safety and efficiency.

## Key Recommendations:

1. **Regular Inspections**
   - Visual checks every 30 days
   - Pressure testing quarterly
   - Full disassembly annually

2. **Critical Components**
   - Replace seals and gaskets
   - Check actuator alignment
   - Verify torque specifications

> **Important**: Always follow manufacturer guidelines and safety protocols.

\`\`\`bash
# Sample maintenance log command
valve-check --pressure 1500psi --type ball-valve
\`\`\`

For more information, visit our [maintenance guide](https://example.com/guide).`,
      tags: ["Valves", "Maintenance", "High-Pressure"],
      feedback: ["Great detailed response!", "Could use more specific torque values"]
    },
    {
      _id: "2", 
      created_at: "2024-12-24T09:15:00Z",
      urgency: "medium",
      source: "Email",
      question: "Which valve type is recommended for corrosive chemical applications?",
      ai_answer: "For corrosive chemical applications, I recommend using **PTFE-lined ball valves** or **Hastelloy gate valves**. These materials offer excellent chemical resistance and durability. Consider factors like temperature, pressure, and specific chemical compatibility when making your selection.",
      tags: ["Valves", "Chemical", "Corrosion"],
      feedback: []
    },
    {
      _id: "3",
      created_at: "2024-12-24T08:45:00Z", 
      urgency: "low",
      source: "Phone",
      question: "What is the difference between gate and ball valves?",
      ai_answer: `## Gate vs Ball Valves

### Gate Valves:
- Linear motion operation
- Full bore when open
- Better for on/off service
- Higher pressure drop when throttling

### Ball Valves:
- Quarter-turn operation  
- Quick opening/closing
- Excellent sealing
- Better for throttling applications

**Recommendation**: Choose based on your specific application requirements.`,
      tags: ["Valves", "Comparison"],
      feedback: ["Very helpful comparison"]
    },
    {
      _id: "4",
      created_at: "2024-12-23T16:20:00Z",
      urgency: "high", 
      source: "Website",
      question: "Emergency valve replacement procedure for steam systems?",
      ai_answer: "EMERGENCY PROCEDURE: 1) Immediately shut off steam supply 2) Allow system to cool and depressurize 3) Isolate the section 4) Follow lockout/tagout procedures 5) Replace valve with identical specifications 6) Pressure test before returning to service. Contact emergency support: 1-800-VALVE-911",
      tags: ["Emergency", "Steam", "Safety"],
      feedback: []
    },
    {
      _id: "5",
      created_at: "2024-12-23T14:10:00Z",
      urgency: "medium",
      source: "Chat",
      question: "Cost comparison between different valve materials?",
      ai_answer: `# Valve Material Cost Analysis

| Material | Relative Cost | Applications |
|----------|---------------|--------------|
| Carbon Steel | 1x (baseline) | General purpose |
| Stainless Steel | 2-3x | Food, pharma |
| Hastelloy | 8-12x | Severe corrosion |
| PTFE Lined | 3-4x | Chemical processing |

*Costs vary by size, pressure rating, and market conditions.*

**Budget Planning Tips:**
- Consider total cost of ownership
- Factor in maintenance costs  
- Evaluate lifecycle performance`,
      tags: ["Cost", "Materials", "Analysis"],
      feedback: ["Excellent cost breakdown"]
    }
  ];

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Enhanced Query Table Test</h1>
        <p className="text-gray-600">
          Testing the enhanced LiveDiffEditor integration with rich text editing, split view, and improved AI response display.
        </p>
      </div>

      <div className="space-y-4 mb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">🚀 New Features Demonstrated:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>Rich Text Indicators:</strong> Green "Rich" badges show responses with markdown formatting</li>
            <li>• <strong>Smart Previews:</strong> AI responses are intelligently truncated with markdown stripped</li>
            <li>• <strong>Enhanced Actions:</strong> "Edit & Feedback" buttons with rich text editor hints</li>
            <li>• <strong>Split View Editing:</strong> Side-by-side editor and live preview in feedback modal</li>
            <li>• <strong>Markdown Conversion:</strong> Automatic conversion with console logging</li>
          </ul>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">🧪 Test Instructions:</h3>
          <ol className="text-sm text-green-700 space-y-1">
            <li>1. Click "Edit & Feedback" on any row to open the enhanced modal</li>
            <li>2. Try editing AI responses with rich text formatting (bold, headers, lists)</li>
            <li>3. Notice the split view with live diff preview</li>
            <li>4. Check browser console for markdown output when saving</li>
            <li>5. Observe the smart preview truncation in the table</li>
          </ol>
        </div>
      </div>

      <QueryTable data={sampleData} />
    </div>
  );
};

export default TestQueryTablePage;
