// lib/api/feedback.ts
import axios from "axios";

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export async function getFeedbackApi() {
  try {
    const response = await axios.get(`${BASE_URL}/feedbacks/`, {
      headers: {
        Accept: "application/json",
      },
    });
    console.log("response", response);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function patchFeedbackApi(id: string, data: any) {
  try {
    const response = await axios.patch(`${BASE_URL}/feedbacks/${id}/append`, data, {
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function updateAiResponseApi(id: string, data: { ai_answer: string }) {
  try {
    const response = await axios.patch(`${BASE_URL}/feedbacks/${id}/update-response`, data, {
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Failed to update AI response:", error);
    throw error;
  }
}
