"use client"

import { useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useDispatch, useSelector } from "react-redux"
import type { RootState, AppDispatch } from "@/store"
import { setActiveQueryId } from "@/store/queriesSlice"
import { loadMessages, startNewChat } from "@/store/chatSlice"
import { setActiveTab, setChatView } from "@/store/uiSlice"
import { AppLayout } from "@/components/app-layout"

export default function QueryPage() {
  const dispatch = useDispatch<AppDispatch>()
  const params = useParams()
  const router = useRouter()
  const queryId = params.id as string

  const { queries, activeQueryId } = useSelector((state: RootState) => state.queries)

  useEffect(() => {
    if (queryId === "new") {
      // Handle new chat
      dispatch(setActiveQueryId(null))
      dispatch(startNewChat())
      dispatch(setActiveTab("chat"))
      dispatch(setChatView("chat"))
      router.push("/")
    } else if (queryId) {
      const queryToLoad = queries.find((q) => q.id === queryId)
      if (queryToLoad) {
        if (activeQueryId !== queryId) {
          dispatch(setActiveQueryId(queryId))
        }
        dispatch(loadMessages(queryToLoad.messages))
        dispatch(setActiveTab("chat"))

        // Determine chat view based on messages
        const hasProcessingMessage = queryToLoad.messages.some(
          (msg) => msg.type === "assistant" && (
            msg.thinkingState?.status === "processing" ||
            (msg.thinkingSteps && Object.values(msg.thinkingSteps).some((step: any) => step.status === 'in-progress'))
          )
        )
        const hasCompletedAssistantMessage = queryToLoad.messages.some(
          (msg) => msg.type === "assistant" && (
            msg.thinkingState?.status === "completed" ||
            (msg.thinkingSteps && Object.values(msg.thinkingSteps).some((step: any) => step.status === 'completed'))
          )
        )

        if (hasProcessingMessage) {
          dispatch(setChatView("thinking"))
        } else if (hasCompletedAssistantMessage) {
          dispatch(setChatView("response"))
        } else {
          dispatch(setChatView("chat"))
        }
      } else {
        // Query ID not found, redirect to home
        console.warn(`Query with ID ${queryId} not found. Redirecting to home.`)
        router.push("/")
      }
    }
  }, [queryId, queries, dispatch, router, activeQueryId])

  return <AppLayout />
}
