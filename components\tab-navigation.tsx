"use client"

import { useDispatch, useSelector } from "react-redux"
import { Button } from "@/components/ui/button"
import { MessageCircle, ListChecks } from "lucide-react"
import { setActiveTab, type ActiveTab } from "@/store/uiSlice"
import type { RootState, AppDispatch } from "@/store"
import { useRouter } from "next/navigation"

export function TabNavigation() {
  const dispatch = useDispatch<AppDispatch>()
  const router = useRouter()
  const activeTab = useSelector((state: RootState) => state.ui.activeTab)

  const handleTabClick = (tab: ActiveTab) => {
    dispatch(setActiveTab(tab))
  }

  return (
    <div className="flex items-center gap-2 sm:gap-6">
      <Button
        variant="ghost"
        className={`flex items-center gap-2 text-base font-semibold px-2 sm:px-3 py-2 h-auto
                    ${activeTab === "chat" ? "text-gray-800" : "text-gray-500 hover:text-gray-700"}`}
        onClick={() => handleTabClick("chat")}
      >
        <div className={`p-1.5 rounded-full ${activeTab === "chat" ? "bg-teal-200" : "bg-gray-100"}`}>
          <MessageCircle className={`h-5 w-5 ${activeTab === "chat" ? "text-teal-600" : "text-gray-500"}`} />
        </div>
        Chat
      </Button>
      <Button
        variant="ghost"
        className={`flex items-center gap-2 text-base font-semibold px-2 sm:px-3 py-2 h-auto
                    ${activeTab === "queries" ? "text-gray-800" : "text-gray-500 hover:text-gray-700"}`}
        onClick={() => handleTabClick("queries")}
      >
        <div className={`p-1.5 rounded-full ${activeTab === "queries" ? "bg-teal-200" : "bg-gray-100"}`}>
          <ListChecks className={`h-5 w-5 ${activeTab === "queries" ? "text-teal-600" : "text-gray-500"}`} />
        </div>
        Queries
      </Button>
    </div>
  )
}
