/**
 * Conversation Context Utilities
 * Handles extraction and formatting of conversation history for WebSocket messages
 */

import type { Message } from "@/types"

export interface ConversationMessage {
  role: "user" | "ai"
  message: string
}

export interface ContextConfig {
  maxPairs: number
  truncationLevels: number[]
  minContentLength: number
}

// Default configuration for conversation context
export const DEFAULT_CONTEXT_CONFIG: ContextConfig = {
  maxPairs: 5,
  truncationLevels: [0.1, 0.25, 0.5, 1.0, 1.0], // From oldest to newest
  minContentLength: 50 // Minimum content length to apply truncation
}

/**
 * Truncate text content based on percentage
 */
function truncateContent(content: string, percentage: number, minLength: number = 50): string {
  if (!content || percentage >= 1) return content

  // Don't truncate very short content
  if (content.length <= minLength) return content

  const targetLength = Math.floor(content.length * percentage)
  if (targetLength <= minLength) {
    // If truncation would make it too short, keep minimum length
    const truncated = content.substring(0, minLength)
    const lastSpaceIndex = truncated.lastIndexOf(' ')

    if (lastSpaceIndex > minLength * 0.7) {
      return truncated.substring(0, lastSpaceIndex) + "..."
    }
    return truncated + "..."
  }

  // Try to truncate at word boundaries when possible
  const truncated = content.substring(0, targetLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')

  if (lastSpaceIndex > targetLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + "..."
  }

  return truncated + "..."
}

/**
 * Extract conversation context from query messages
 * Returns up to maxPairs message pairs (user + ai) with truncation applied to older AI responses
 */
export function extractConversationContext(
  messages: Message[],
  currentUserMessage: string,
  config: ContextConfig = DEFAULT_CONTEXT_CONFIG
): ConversationMessage[] {
  const conversationHistory: ConversationMessage[] = []

  // Filter out messages that don't have content (e.g., processing messages)
  const validMessages = messages.filter(msg =>
    msg.content && msg.content.trim().length > 0
  )

  // Check if the current user message is already the last message in the array
  const lastMessage = validMessages[validMessages.length - 1]
  const isCurrentMessageAlreadyIncluded = lastMessage &&
    lastMessage.type === 'user' &&
    lastMessage.content === currentUserMessage

  // If current message is already included, use all messages; otherwise exclude the last message
  const messagesToProcess = isCurrentMessageAlreadyIncluded
    ? validMessages
    : validMessages.slice(0, -1) // Remove the last message if it's not the current one

  // Group messages into pairs (user message followed by assistant response)
  const messagePairs: { user: Message; assistant?: Message }[] = []

  for (let i = 0; i < messagesToProcess.length; i++) {
    const message = messagesToProcess[i]

    if (message.type === 'user') {
      // Look for the next assistant message
      const nextAssistant = messagesToProcess.slice(i + 1).find(m => m.type === 'assistant')
      messagePairs.push({
        user: message,
        assistant: nextAssistant
      })
    }
  }

  // Take the last maxPairs pairs (most recent conversations)
  const recentPairs = messagePairs.slice(-config.maxPairs)

  // Apply truncation strategy to AI responses using config
  const { truncationLevels, minContentLength } = config

  recentPairs.forEach((pair, index) => {
    // Add user message (never truncated)
    conversationHistory.push({
      role: "user",
      message: pair.user.content
    })

    // Add assistant message with appropriate truncation
    if (pair.assistant) {
      const truncationLevel = truncationLevels[index] || 1.0
      const assistantContent = truncationLevel < 1.0
        ? truncateContent(pair.assistant.content, truncationLevel, minContentLength)
        : pair.assistant.content

      conversationHistory.push({
        role: "ai",
        message: assistantContent
      })
    }
  })

  // Only add the current user message if it's not already included
  if (!isCurrentMessageAlreadyIncluded) {
    conversationHistory.push({
      role: "user",
      message: currentUserMessage
    })
  }

  return conversationHistory
}

/**
 * Format conversation history for WebSocket API
 * Converts internal format to the expected WebSocket message format
 */
export function formatConversationForWebSocket(
  conversationHistory: ConversationMessage[]
): Array<{ role: string; message: string }> {
  return conversationHistory.map(msg => ({
    role: msg.role,
    message: msg.message
  }))
}

/**
 * Get conversation context for a specific query
 * Main function to be used by WebSocket middleware
 */
export function getConversationContextForQuery(
  queryMessages: Message[],
  currentUserMessage: string
): Array<{ role: string; message: string }> {
  const context = extractConversationContext(queryMessages, currentUserMessage)
  return formatConversationForWebSocket(context)
}
