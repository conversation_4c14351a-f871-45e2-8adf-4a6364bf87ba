/**
 * WebSocket Service for AI Chat App
 * Handles connection to WebSocket server
 * Manages connection lifecycle, reconnection, and message handling
 */

export interface WebSocketMessage {
  query: Array<{ role: string; message: string }>;
  use_web: boolean;
  use_database: boolean;
  use_unstructured: boolean;
  use_feedback: boolean;
}

export interface IncomingMessage {
  agent_invoked?: 'user_intent_agent' | 'web_search_agent' | 'product_unstructured_agent' | 'database_search_agent' | 'feedback_agent' | 'supervisor_agent';
  agent_status?: 'starting' | 'completed';
  agent_message?: string;
  agent_result?: any;
  status?: 'error';
  message?: string;
  final_response?: string;
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export interface WebSocketServiceCallbacks {
  onMessage: (message: IncomingMessage) => void;
  onConnectionChange: (status: ConnectionStatus) => void;
  onError: (error: string) => void;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private callbacks: WebSocketServiceCallbacks | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isManuallyDisconnected = false;
  private connectionStatus: ConnectionStatus = 'disconnected';

  private readonly WS_URL = process.env.NEXT_PUBLIC_WS_URL

  /**
   * Initialize the WebSocket service with callbacks
   */
  initialize(callbacks: WebSocketServiceCallbacks) {
    this.callbacks = callbacks;
  }

  /**
   * Connect to the WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.isManuallyDisconnected = false;
      this.setConnectionStatus('connecting');

      try {
        if (!this.WS_URL) {
          throw new Error('WebSocket URL is not defined');
        }
        this.ws = new WebSocket(this.WS_URL);

        this.ws.onopen = () => {
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          this.setConnectionStatus('connected');
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: IncomingMessage = JSON.parse(event.data);
            this.callbacks?.onMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
            this.callbacks?.onError('Failed to parse server message');
          }
        };

        this.ws.onclose = () => {
          this.setConnectionStatus('disconnected');

          if (!this.isManuallyDisconnected && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.setConnectionStatus('error');
          this.callbacks?.onError('WebSocket connection error');
          reject(new Error('WebSocket connection failed'));
        };

      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        this.setConnectionStatus('error');
        this.callbacks?.onError('Failed to create WebSocket connection');
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    this.isManuallyDisconnected = true;
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.setConnectionStatus('disconnected');
  }

  /**
   * Send a message through the WebSocket
   */
  sendMessage(message: WebSocketMessage): boolean {
    if (this.ws?.readyState !== WebSocket.OPEN) {
      console.error('WebSocket is not connected');
      this.callbacks?.onError('WebSocket is not connected');
      return false;
    }

    try {
      const messageString = JSON.stringify(message);
      this.ws.send(messageString);
      return true;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      this.callbacks?.onError('Failed to send message');
      return false;
    }
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Format outgoing message for the WebSocket API
   * @param query - Current user query string
   * @param sources - Source configuration
   * @param conversationHistory - Optional conversation history (defaults to single user message)
   */
  formatOutgoingMessage(
    query: string,
    sources: {
      web: boolean;
      database: boolean;
      unstructured: boolean;
      feedback: boolean;
    },
    conversationHistory?: Array<{ role: string; message: string }>
  ): WebSocketMessage {
    // Use conversation history if provided, otherwise default to single user message
    const queryMessages = conversationHistory || [{ role: "user", message: query }];

    return {
      query: queryMessages,
      use_web: sources.web,
      use_database: sources.database,
      use_unstructured: sources.unstructured,
      use_feedback: sources.feedback
    };
  }

  /**
   * Map agent names to UI-friendly display names
   */
  getAgentDisplayName(agentName: string): string {
    const agentDisplayNames: Record<string, string> = {
      'user_intent_agent': 'Completed User Analysis',
      'web_search_agent': 'Searching Web',
      'product_unstructured_agent': 'Searching Ross Emails, PDFs, Docs and FAQs',
      'database_search_agent': 'Searching Ross Database',
      'feedback_agent': 'Searching Ross Senior Team Feedbacks',
      'product_structured_agent': 'Searching Ross Product Catalog'
    };

    return agentDisplayNames[agentName] || agentName;
  }

  private setConnectionStatus(status: ConnectionStatus) {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.callbacks?.onConnectionChange(status);
    }
  }

  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.maxReconnectAttempts;
  }

  private scheduleReconnect() {
    this.clearReconnectTimer();

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect().catch(() => {
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
      });
    }, this.reconnectDelay);
  }

  private clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Cleanup method to be called when the service is no longer needed
   */
  cleanup() {
    this.disconnect();
    this.callbacks = null;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
