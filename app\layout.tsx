// import type React from "react"
// import type { Metadata } from "next"
// import { Inter } from "next/font/google"
// import "./globals.css"
// import { Providers } from "@/components/providers" 

// const inter = Inter({ subsets: ["latin"] })

// export const metadata: Metadata = {
//   title: "ROSS AI Chat App",
//   description: "AI Chat Application",
//     generator: 'v0.dev'
// }

// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode
// }>) {
//   return (
//     <html lang="en">
//       <body className={inter.className}>
//         <Providers>
//           {" "}
//           {/* Wrap with Providers */}
//           {children}
//         </Providers>
//       </body>
//     </html>
//   )
// }

import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import ReactQueryProvider from "@/components/react-query-provider"; // 👈 import

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "ROSS AI Chat App",
  description: "AI Chat Application",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ReactQueryProvider>
          <Providers>
            {children}
          </Providers>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
