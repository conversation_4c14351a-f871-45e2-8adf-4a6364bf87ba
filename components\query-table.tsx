"use client";
import { FaReply } from "react-icons/fa";
import moment from "moment";
import { useState } from "react";
import { SideModal } from "./ui/modal";
import Feedback from "./feedback";

// const data = [
//   {
//     date: "2024-03-15",
//     priority: "High",
//     source: "Website",
//     query: "Which valve do you recommend...",
//     response: "For an application operating at...",
//     tags: ["Valves"],
//   },
//     {
//     date: "2024-03-15",
//     priority: "High",
//     source: "Website",
//     query: "Which valve do you recommend...",
//     response: "For an application operating at...",
//     tags: ["Valves"],
//   },
//     {
//     date: "2024-03-15",
//     priority: "High",
//     source: "Website",
//     query: "Which valve do you recommend...",
//     response: "For an application operating at...",
//     tags: ["Valves"],
//   },
//   // Add more rows as needed
// ];

const priorityColors: Record<string, string> = {
  high: "bg-red-100 text-red-600",
  medium: "bg-yellow-100 text-yellow-600",
  low: "bg-green-100 text-green-600",
};

type QueryItem = {
  feedback: string[];
  created_at: string;
  urgency: string;
  source: string;
  question: string;
  ai_answer: string;
  tags: string[];
  _id: string;
};

interface QueryTableProps {
  data: QueryItem[];
}

export default function QueryTable({ data }: QueryTableProps) {
  const [open, setOpen] = useState(false);
  const [dataItem, setDataItem] = useState<QueryItem>({} as QueryItem);
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Queries</h2>
        <input
          type="text"
          placeholder="Search"
          className="border border-gray-300 rounded px-3 py-1 text-sm"
        />
      </div>

      <div className="overflow-auto">
        <table className="w-full text-sm text-left border-separate border-spacing-y-2">
          <thead className="text-gray-500 font-medium">
            <tr>
              <th>Date</th>
              <th>Priority</th>
              <th>Source</th>
              <th>Query</th>
              <th>AI Draft Response</th>
              <th>Tags</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item: QueryItem, idx) => (
              <tr key={idx} className="bg-gray-50 hover:bg-gray-100 rounded">
                <td className="py-2 px-2">
                  {moment
                    .utc(item.created_at)
                    .local()
                    .format("YYYY-MM-DD HH:mm:ss")}
                </td>
                <td className="py-2 px-2">
                  <span
                    className={`text-xs px-2 py-1 rounded-full font-semibold ${
                      priorityColors[item.urgency]
                    }`}
                  >
                    {item?.urgency}
                  </span>
                </td>
                <td className="py-2 px-2">{item.source}</td>
                <td className="py-2 px-2 text-ellipsis overflow-hidden whitespace-nowrap max-w-[180px]">
                  {item.question}
                </td>
                <td className="py-2 px-2 text-ellipsis overflow-hidden whitespace-nowrap max-w-[200px]">
                  {item.ai_answer}
                </td>
                <td className="py-2 px-2">
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full mr-1">
                    {item.tags[0]}
                  </span>
                  {item.tags.length - 1 > 0 && (
                    <span className="text-xs text-gray-500">
                      {} and {item.tags.length - 1} more
                    </span>
                  )}
                </td>
                <td
                  className="py-2 px-2 cursor-pointer text-xs hover:underline"
                  onClick={() => {
                    setOpen(true);
                    setDataItem(item);
                  }}
                  style={{ color: "#00B2A1" }}
                >
                  <FaReply />
                  Feedback
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <SideModal isOpen={open} onClose={() => setOpen(false)} title="Feedback">
        <Feedback data={dataItem} setOpen={setOpen}/>
      </SideModal>

      <div className="flex justify-between items-center mt-4 text-sm text-gray-500">
        <span>Showing 1 to 7 of 20 results</span>
        <div className="flex gap-2">
          <button className="px-2 py-1 border rounded hover:bg-gray-100">
            &lt;
          </button>
          <button className="px-2 py-1 border rounded hover:bg-gray-100">
            &gt;
          </button>
        </div>
      </div>
    </div>
  );
}
