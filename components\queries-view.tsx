import { useState } from "react";
import { QueriesList } from "./queries-list";
import { SideModal } from "./ui/modal";
import Feedback from "./feedback";
import QueryTable from "./query-table";
import { useQuery } from "@tanstack/react-query";
import { getFeedbackApi } from "@/lib/api";
import { Loader2Icon } from "lucide-react";

export function QueriesView() {
  const [open, setOpen] = useState(false);
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["feedback"],
    queryFn: getFeedbackApi,
  });

  if (isLoading) return <div className="flex justify-center items-center h-screen"> <Loader2Icon className="h-10 w-10 animate-spin" /> </div>;
  if (isError) return <div>Error: {(error as Error).message}</div>;
  console.log("data", data);

  return (
    <div>
      {/* <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Past Conversations
      </h2> */}
      {/* <div className="p-4">
        <button
          onClick={() => setOpen(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Open Side Modal
        </button>

        <SideModal
          isOpen={open}
          onClose={() => setOpen(false)}
          title="Feedback"
        >
          <Feedback />
        </SideModal>
      </div> */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        {/* <QueriesList /> */}

        <QueryTable data={data} />
      </div>
    </div>
  );
}
