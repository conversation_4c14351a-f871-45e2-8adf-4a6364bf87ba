/**
 * Utility functions for handling PDF file paths and operations
 */

/**
 * Maps a PDF name from agent response to the actual file path in the public directory
 * @param pdfName - The PDF name from the agent response (e.g., "document.pdf")
 * @returns The full path to the PDF file in the public directory
 */
export function resolvePDFPath(pdfName: string): string {
  // Remove any path separators and ensure we have just the filename
  const cleanFileName = pdfName.replace(/^.*[\\\/]/, '')

  // Ensure the filename has .pdf extension
  const finalFileName = cleanFileName.toLowerCase().endsWith('.pdf')
    ? cleanFileName
    : `${cleanFileName}.pdf`

  // Construct the path to the PDF in the public directory
  // Note: In Next.js, files in /public are served from the root
  const pdfPath = `/pdfs/${finalFileName}`

  console.log('Resolved PDF path:', { input: pdfName, output: pdfPath })
  return pdfPath
}

/**
 * Checks if a PDF file exists by attempting to fetch its headers
 * @param pdfPath - The path to the PDF file
 * @returns Promise that resolves to true if the file exists, false otherwise
 */
export async function checkPDFExists(pdfPath: string): Promise<boolean> {
  try {
    const response = await fetch(pdfPath, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    console.error('Error checking PDF existence:', error)
    return false
  }
}

/**
 * Validates and sanitizes a PDF name
 * @param pdfName - The PDF name to validate
 * @returns The sanitized PDF name or null if invalid
 */
export function validatePDFName(pdfName: string): string | null {
  if (!pdfName || typeof pdfName !== 'string') {
    console.warn('Invalid PDF name provided:', pdfName)
    return null
  }

  // Keep original filename but remove path separators
  const cleanName = pdfName.replace(/^.*[\\\/]/, '')

  // Basic validation - ensure it's not empty after cleaning
  if (!cleanName.trim()) {
    console.warn('PDF name is empty after cleaning:', pdfName)
    return null
  }

  // Ensure it has a PDF extension
  const finalName = cleanName.toLowerCase().endsWith('.pdf')
    ? cleanName
    : `${cleanName}.pdf`

  console.log('Validated PDF name:', { input: pdfName, output: finalName })
  return finalName
}

/**
 * Extracts PDF information from a product unstructured source
 * @param source - The source object from product_unstructured_agent
 * @returns Object containing PDF path, name, and page range
 */
export function extractPDFInfo(source: {
  pdf: string
  page_start: number
  page_end: number
}) {
  console.log('Extracting PDF info from source:', source)

  const validatedName = validatePDFName(source.pdf)

  if (!validatedName) {
    throw new Error(`Invalid PDF name: ${source.pdf}`)
  }

  const result = {
    pdfPath: resolvePDFPath(validatedName),
    pdfName: validatedName,
    pageRange: {
      start: Math.max(1, source.page_start || 1),
      end: Math.max(source.page_start || 1, source.page_end || 1)
    }
  }

  console.log('Extracted PDF info:', result)
  return result
}

/**
 * Creates a download link for a PDF
 * @param pdfPath - The path to the PDF file
 * @param pdfName - The name for the downloaded file
 */
export function downloadPDF(pdfPath: string, pdfName: string): void {
  const link = document.createElement('a')
  link.href = pdfPath
  link.download = pdfName
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
