"use client"

import React from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { PDFViewerModal } from './pdf-viewer-modal'
import { closePDFModal } from '@/store/pdfModalSlice'
import type { RootState, AppDispatch } from '@/store'

/**
 * Provider component that connects the PDF modal to Redux state
 * This should be included in the main app layout to handle PDF modal display
 */
export function PDFModalProvider() {
  const dispatch = useDispatch<AppDispatch>()
  const pdfModal = useSelector((state: RootState) => state.pdfModal)

  const handleClose = () => {
    dispatch(closePDFModal())
  }

  if (!pdfModal.isOpen || !pdfModal.pdfPath || !pdfModal.pdfName) {
    return null
  }

  return (
    <PDFViewerModal
      isOpen={pdfModal.isOpen}
      onClose={handleClose}
      pdfPath={pdfModal.pdfPath}
      pdfName={pdfModal.pdfName}
      initialPage={pdfModal.initialPage}
      pageRange={pdfModal.pageRange}
    />
  )
}
